<script lang="ts">
import {T} from "@threlte/core";
import {type IntersectionEvent} from "@threlte/extras";
import * as THREE from "three";

let {
    url,
    position = [0, 0, 0],
    scale = [1, 1, 1],
    meshRef = $bindable(),
    onclick,
}: {
    url: string,
    position?: [number, number, number],
    scale?: [number, number, number],
    meshRef?: THREE.Mesh | null,
    onclick?: (event: IntersectionEvent<PointerEvent>) => void,
} = $props();

let texture = $state<THREE.Texture | null>(null);
let aspectRatio = $derived.by(() => {
    if (texture === null) return 1;
    return texture.image.width / texture.image.height;
});

const loadTexture = (url: string) => {
    return new Promise<THREE.Texture>((resolve, reject) => {
        const loader = new THREE.TextureLoader();

        loader.load(
            url,
            texture => {
                if (texture === null) return;

                texture.colorSpace = THREE.SRGBColorSpace;
                texture.premultiplyAlpha = false;
                resolve(texture);
            },
            undefined,
            error => {
                reject(error);
            },
        );
    });
};

$effect(() => {
    loadTexture(url).then((loadedTexture) => {
        texture = loadedTexture;
    });
});
</script>

{#if texture !== null}
    <T.Mesh
        bind:ref={() => undefined, value => meshRef = value ?? null}
        {position}
        {scale}
        castShadow
        receiveShadow
        {onclick}
    >
        <T.PlaneGeometry args={[aspectRatio, 1]} />
        <T.MeshStandardMaterial
            map={texture}
            side={THREE.DoubleSide}
            transparent
            alphaTest={0.01}
        />
    </T.Mesh>
{/if}
<script lang="ts">
import type { Character } from "$lib/types/Character";
import { T } from "@threlte/core";
import * as THREE from "three";
import ImagePlane from "./ImagePlane.svelte";
    import type { IntersectionEvent } from "@threlte/extras";

let {
    character = $bindable(),
}: {
    character: Character,
} = $props();

let segmentStart = $state<{ x: number, y: number } | null>(null);
let segmentEnd = $state<{ x: number, y: number } | null>(null);
let imagePlaneRef = $state<THREE.Mesh | null>(null);
const planeBottomLeftCorner = $state(null);

const handleImageClick = (event: IntersectionEvent<PointerEvent>) => {

    console.log(event);
    return;
    // Get the canvas element and calculate mouse position

    // Convert normalized coordinates to world coordinates
    // The image plane is 1 unit tall, so we need to scale by 0.5 to get the range [-0.5, 0.5]
    // For the x coordinate, we need to account for the aspect ratio
    const geometry = imagePlaneRef.geometry as THREE.PlaneGeometry;
    const parameters = geometry.parameters;
    const aspectRatio = parameters.width / parameters.height;

    const imageX = x * (aspectRatio / 2);
    const imageY = y * 0.5;

    if (!segmentStart) {
        // Set the start point
        segmentStart = { x: imageX, y: imageY };
    } else if (!segmentEnd) {
        // Set the end point and create the segment
        segmentEnd = { x: imageX, y: imageY };

        // Calculate segment length
        const length = Math.sqrt(
            Math.pow(segmentEnd.x - segmentStart.x, 2) +
            Math.pow(segmentEnd.y - segmentStart.y, 2)
        );

        // Update character's baseline
        character.sizeBaseline = {
            start: { x: segmentStart.x, y: segmentStart.y },
            end: { x: segmentEnd.x, y: segmentEnd.y },
            length
        };

        // Reset for next segment
        segmentStart = null;
        segmentEnd = null;
    }
};
</script>

<!-- 3D Scene -->
<ImagePlane
    url={character.imageUrl}
    position={[0, 0.5, 0]}
    bind:meshRef={imagePlaneRef}
    onclick={handleImageClick}
/>

<!-- Visualize current segment being created -->
{#if segmentStart && !segmentEnd}
    <T.Mesh position={[segmentStart.x, segmentStart.y, 0.01]}>
        <T.SphereGeometry args={[0.02]} />
        <T.MeshBasicMaterial color="#ff0000" />
    </T.Mesh>
{/if}

<!-- Visualize completed baseline segment -->
{#if character.sizeBaseline}
    <!-- Start point -->
    <T.Mesh position={[character.sizeBaseline.start.x, character.sizeBaseline.start.y, 0.01]}>
        <T.SphereGeometry args={[0.02]} />
        <T.MeshBasicMaterial color="#00ff00" />
    </T.Mesh>

    <!-- End point -->
    <T.Mesh position={[character.sizeBaseline.end.x, character.sizeBaseline.end.y, 0.01]}>
        <T.SphereGeometry args={[0.02]} />
        <T.MeshBasicMaterial color="#00ff00" />
    </T.Mesh>

    <!-- Line between points -->
    <T.Line>
        <T.BufferGeometry>
            <T.BufferAttribute
                attach="attributes.position"
                array={new Float32Array([
                    character.sizeBaseline.start.x, character.sizeBaseline.start.y, 0.01,
                    character.sizeBaseline.end.x, character.sizeBaseline.end.y, 0.01
                ])}
                count={2}
                itemSize={3}
            />
        </T.BufferGeometry>
        <T.LineBasicMaterial color="#00ff00" linewidth={2} />
    </T.Line>
{/if}